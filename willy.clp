(deffacts hechosIniciales
  (casilla-visitada 0 0)
  (casilla-actual 0 0)
  (NumPasos 0)
  (maxPasos 1000)
  (ultimo-movimiento ninguno)
)

(defrule moverNorteNoVisitadoSeguro
  (directions $? north $?)
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento $?)
  (maxPasos ?max&:(< ?mov ?max))
  (not (percepts $?))  
  (not (casilla-visitada ?x2&:(= (- ?x 1) ?x2) ?y))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (casilla-actual (- ?x 1) ?y))
  (assert (casilla-visitada (- ?x 1) ?y))
  (assert (ultimo-movimiento north))
  (moveWilly north)
)

(defrule moverSurNoVisitadoSeguro
  (directions $? south $?)
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento $?)
  (maxPasos ?max&:(< ?mov ?max))
  (not (percepts $?))
  (not (casilla-visitada ?x2&:(= (+ ?x 1) ?x2) ?y))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (casilla-actual (+ ?x 1) ?y))
  (assert (casilla-visitada (+ ?x 1) ?y))
  (assert (ultimo-movimiento south))
  (moveWilly south)
)

(defrule moverEsteNoVisitadoSeguro
  (directions $? east $?)
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento $?)
  (maxPasos ?max&:(< ?mov ?max))
  (not (percepts $?))
  (not (casilla-visitada ?x ?y2&:(= (+ ?y 1) ?y2)))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (casilla-actual ?x (+ ?y 1)))
  (assert (casilla-visitada ?x (+ ?y 1)))
  (assert (ultimo-movimiento east))
  (moveWilly east)
)

(defrule moverOesteNoVisitadoSeguro
  (directions $? west $?)
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento $?)
  (maxPasos ?max&:(< ?mov ?max))
  (not (percepts $?))
  (not (casilla-visitada ?x ?y2&:(= (- ?y 1) ?y2)))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (casilla-actual ?x (- ?y 1)))
  (assert (casilla-visitada ?x (- ?y 1)))
  (assert (ultimo-movimiento west))
  (moveWilly west)
)


(defrule retrocederDePeligro
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento ?dir&~ninguno)
  (maxPasos ?max&:(< ?mov ?max))
  (percepts $? )  
  (directions $? ?opuesto $?)
  (test (or (and (eq ?dir north) (eq ?opuesto south))
            (and (eq ?dir south) (eq ?opuesto north))
            (and (eq ?dir east) (eq ?opuesto west))
            (and (eq ?dir west) (eq ?opuesto east))))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (ultimo-movimiento ?opuesto))
  (if (eq ?dir north) then
      (assert (casilla-actual (+ ?x 1) ?y)))
  (if (eq ?dir south) then
      (assert (casilla-actual (- ?x 1) ?y)))
  (if (eq ?dir east) then
      (assert (casilla-actual ?x (- ?y 1))))
  (if (eq ?dir west) then
      (assert (casilla-actual ?x (+ ?y 1))))
  (moveWilly ?opuesto)
)


; Disparar solo cuando hay Noise pero no Pull, y con estrategia
(defrule dispararAlienígenaEstrategico
  (hasLaser)
  (percepts Noise)  ; Solo Noise, no Pull
  (directions $? ?dir $?)
  =>
  (fireLaser ?dir)
)

; Disparar cuando hay ambos pero priorizando alejarse del Pull
(defrule dispararConPeligroMixto
  (hasLaser)
  (percepts $? Noise $? Pull $?)
  (directions $? ?dir $?)
  ; Solo disparar si no hay otra opción segura
  (not (directions $? ?dirSegura&~?dir $?))
  =>
  (fireLaser ?dir)
)

; Marcar casilla actual como segura cuando no hay peligros
(defrule marcarCasillaSegura
  (not (percepts $?))  ; No hay percepciones de peligro
  ?h1<-(casilla-actual ?x ?y)
  (not (casilla-segura ?x ?y))  ; No está ya marcada como segura
  =>
  (assert (casilla-segura ?x ?y))
)

; Marcar celdas peligrosas con más detalle
(defrule marcarCeldaPeligrosa
  (percepts $? ?peligro $?)
  ?h1<-(casilla-actual ?x ?y)
  =>
  (if (eq ?peligro Pull) then
      (assert (celda-peligrosa agujero ?x ?y))
      ; Marcar celdas adyacentes como posibles peligros
      (assert (posible-peligro agujero (- ?x 1) ?y))
      (assert (posible-peligro agujero (+ ?x 1) ?y))
      (assert (posible-peligro agujero ?x (- ?y 1)))
      (assert (posible-peligro agujero ?x (+ ?y 1))))
  (if (eq ?peligro Noise) then
      (assert (celda-peligrosa alien ?x ?y))
      ; Marcar celdas adyacentes como posibles peligros
      (assert (posible-peligro alien (- ?x 1) ?y))
      (assert (posible-peligro alien (+ ?x 1) ?y))
      (assert (posible-peligro alien ?x (- ?y 1)))
      (assert (posible-peligro alien ?x (+ ?y 1))))
)

; Inferir casillas seguras adyacentes cuando estamos en una casilla segura
(defrule inferirCasillasSeguras
  (casilla-segura ?x ?y)
  (casilla-actual ?x ?y)
  (not (percepts $?))  ; Confirmamos que no hay peligros
  =>
  ; Las casillas adyacentes son potencialmente más seguras
  (assert (casilla-probablemente-segura (- ?x 1) ?y))
  (assert (casilla-probablemente-segura (+ ?x 1) ?y))
  (assert (casilla-probablemente-segura ?x (- ?y 1)))
  (assert (casilla-probablemente-segura ?x (+ ?y 1)))
)

; Explorar casillas probablemente seguras - Norte
(defrule explorarCasillaProbablementeSeguraNorte
    (declare (salience 10))
    (directions $? north $?)
    ?h1 <- (NumPasos ?mov)
    ?h2 <- (casilla-actual ?x ?y)
    ?h3 <- (ultimo-movimiento $?)
    (maxPasos ?max&:(< ?mov ?max))
    (not (percepts $?))
    (not (casilla-visitada ?x2&:(= (- ?x 1) ?x2) ?y))
    (casilla-probablemente-segura ?x2&:(= (- ?x 1) ?x2) ?y)
    (not (celda-peligrosa $? ?x2&:(= (- ?x 1) ?x2) ?y))
    =>
    (retract ?h1 ?h2 ?h3)
    (assert (NumPasos (+ ?mov 1)))
    (assert (ultimo-movimiento north))
    (assert (casilla-actual (- ?x 1) ?y))
    (assert (casilla-visitada (- ?x 1) ?y))
    (moveWilly north)
)

; Explorar casillas probablemente seguras - Sur
(defrule explorarCasillaProbablementeSeguraSur
    (declare (salience 10))
    (directions $? south $?)
    ?h1 <- (NumPasos ?mov)
    ?h2 <- (casilla-actual ?x ?y)
    ?h3 <- (ultimo-movimiento $?)
    (maxPasos ?max&:(< ?mov ?max))
    (not (percepts $?))
    (not (casilla-visitada ?x2&:(= (+ ?x 1) ?x2) ?y))
    (casilla-probablemente-segura ?x2&:(= (+ ?x 1) ?x2) ?y)
    (not (celda-peligrosa $? ?x2&:(= (+ ?x 1) ?x2) ?y))
    =>
    (retract ?h1 ?h2 ?h3)
    (assert (NumPasos (+ ?mov 1)))
    (assert (ultimo-movimiento south))
    (assert (casilla-actual (+ ?x 1) ?y))
    (assert (casilla-visitada (+ ?x 1) ?y))
    (moveWilly south)
)

; Explorar casillas probablemente seguras - Este
(defrule explorarCasillaProbablementeSeguraEste
    (declare (salience 10))
    (directions $? east $?)
    ?h1 <- (NumPasos ?mov)
    ?h2 <- (casilla-actual ?x ?y)
    ?h3 <- (ultimo-movimiento $?)
    (maxPasos ?max&:(< ?mov ?max))
    (not (percepts $?))
    (not (casilla-visitada ?x ?y2&:(= (+ ?y 1) ?y2)))
    (casilla-probablemente-segura ?x ?y2&:(= (+ ?y 1) ?y2))
    (not (celda-peligrosa $? ?x ?y2&:(= (+ ?y 1) ?y2)))
    =>
    (retract ?h1 ?h2 ?h3)
    (assert (NumPasos (+ ?mov 1)))
    (assert (ultimo-movimiento east))
    (assert (casilla-actual ?x (+ ?y 1)))
    (assert (casilla-visitada ?x (+ ?y 1)))
    (moveWilly east)
)

; Explorar casillas probablemente seguras - Oeste
(defrule explorarCasillaProbablementeSeguraOeste
    (declare (salience 10))
    (directions $? west $?)
    ?h1 <- (NumPasos ?mov)
    ?h2 <- (casilla-actual ?x ?y)
    ?h3 <- (ultimo-movimiento $?)
    (maxPasos ?max&:(< ?mov ?max))
    (not (percepts $?))
    (not (casilla-visitada ?x ?y2&:(= (- ?y 1) ?y2)))
    (casilla-probablemente-segura ?x ?y2&:(= (- ?y 1) ?y2))
    (not (celda-peligrosa $? ?x ?y2&:(= (- ?y 1) ?y2)))
    =>
    (retract ?h1 ?h2 ?h3)
    (assert (NumPasos (+ ?mov 1)))
    (assert (ultimo-movimiento west))
    (assert (casilla-actual ?x (- ?y 1)))
    (assert (casilla-visitada ?x (- ?y 1)))
    (moveWilly west)
)
