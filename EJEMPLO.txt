(deffacts hechosIniciales
  (casilla-visitada 0 0)
  (casilla-actual 0 0)
  (NumPasos 0)
  (maxPasos 1000)
  (ultimo-movimiento ninguno)
)

(defrule moverNorteNoVisitadoSeguro
  (directions $? north $?)
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento $?)
  (maxPasos ?max&:(< ?mov ?max))
  (not (percepts $?))  
  (not (casilla-visitada ?x2&:(= (- ?x 1) ?x2) ?y))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (casilla-actual (- ?x 1) ?y))
  (assert (casilla-visitada (- ?x 1) ?y))
  (assert (ultimo-movimiento north))
  (moveWilly north)
)

(defrule moverSurNoVisitado
(directions $? south $?)
?h1<-(NumPasos ?mov&:(< ?mov 999))
?h2<-(casilla-actual ?x ?y)
(percepts) 
(not (casilla-visitada ?x2&:(= (+ ?x 1) ?x2) ?y)) 
=>
(retract ?h1 ?h2)
(assert (NumPasos (+ ?mov 1))) 
(assert (casilla-actual (+ ?x 1) ?y))
(assert (casilla-visitada (+ ?x 1) ?y))
(moveWilly south)
)

(defrule moverEsteNoVisitado
(directions $? east $?)
?h1<-(NumPasos ?mov&:(< ?mov 999))
?h2<-(casilla-actual ?x ?y)
(percepts) 
=>
(retract ?h1 ?h2)
(assert (NumPasos (+ ?mov 1))) ; 
incrementando el valor actual en 1
(assert (casilla-actual ?x (+ ?y 1)))
(assert (casilla-visitada ?x (+ ?y 1)))
(moveWilly east)
)

(defrule moverOesteNoVisitado
(directions $? west $?)
?h1<-(NumPasos ?mov&:(< ?mov 999))
?h2<-(casilla-actual ?x ?y)
(percepts) 
(not (casilla-visitada ?x ?y2&:(= (- ?y 1) ?y2))) 
=>
(retract ?h1 ?h2)
(assert (NumPasos (+ ?mov 1))) 
(assert (casilla-actual ?x (- ?y 1)))
(assert (casilla-visitada ?x (- ?y 1)))
(moveWilly west)
)


(defrule retrocederDePeligro
  ?h1<-(NumPasos ?mov)
  ?h2<-(casilla-actual ?x ?y)
  ?h3<-(ultimo-movimiento ?dir&~ninguno)
  (maxPasos ?max&:(< ?mov ?max))
  (percepts $? )  
  (directions $? ?opuesto $?)
  (test (or (and (eq ?dir north) (eq ?opuesto south))
            (and (eq ?dir south) (eq ?opuesto north))
            (and (eq ?dir east) (eq ?opuesto west))
            (and (eq ?dir west) (eq ?opuesto east))))
  =>
  (retract ?h1 ?h2 ?h3)
  (assert (NumPasos (+ ?mov 1)))
  (assert (ultimo-movimiento ?opuesto))
  (if (eq ?dir north) then
      (assert (casilla-actual (+ ?x 1) ?y)))
  (if (eq ?dir south) then
      (assert (casilla-actual (- ?x 1) ?y)))
  (if (eq ?dir east) then
      (assert (casilla-actual ?x (- ?y 1))))
  (if (eq ?dir west) then
      (assert (casilla-actual ?x (+ ?y 1))))
  (moveWilly ?opuesto)
)


(defrule dispararAlienígena
  (hasLaser)
  (percepts $? Noise $?)
  (directions $? ?dir $?)
  (not (percepts $? Pull $?))  
  =>
  (fireLaser ?dir)
)

; Marcar celdas peligrosas con más detalle
(defrule marcarCeldaPeligrosa
  (percepts $? ?peligro $?)
  ?h1<-(casilla-actual ?x ?y)
  =>
  (if (eq ?peligro Pull) then
      (assert (celda-peligrosa agujero ?x ?y))
      ; Marcar celdas adyacentes como posibles peligros
      (assert (posible-peligro agujero (- ?x 1) ?y))
      (assert (posible-peligro agujero (+ ?x 1) ?y))
      (assert (posible-peligro agujero ?x (- ?y 1)))
      (assert (posible-peligro agujero ?x (+ ?y 1))))
  (if (eq ?peligro Noise) then
      (assert (celda-peligrosa alien ?x ?y))
      ; Marcar celdas adyacentes como posibles peligros
      (assert (posible-peligro alien (- ?x 1) ?y))
      (assert (posible-peligro alien (+ ?x 1) ?y))
      (assert (posible-peligro alien ?x (- ?y 1)))
      (assert (posible-peligro alien ?x (+ ?y 1))))
)

(defrule explorarCeldaNoVisitada
    (directions $? north $?)   ; Asegura que esta regla solo se active para la dirección norte
    ?h1 <- (NumPasos ?mov)
    ?h2 <- (casilla-actual ?x ?y)
    ?h3 <- (ultimo-movimiento $?)
    (maxPasos ?max&:(< ?mov ?max))
    (not (percepts $?))         ; No hay peligro (ninguna percepción) en la celda actual

    ; Condiciones para la celda al NORTE: (x-1, y)
    ; 1. La celda (- ?x 1) ?y NO ha sido visitada
    (not (casilla-visitada =(- ?x 1) ?y))
    ; 2. La celda (- ?x 1) ?y NO está marcada como peligrosa
    (not (celda-peligrosa $? =(- ?x 1) ?y))
    =>
    (retract ?h1 ?h2 ?h3)
    (assert (NumPasos (+ ?mov 1)))
    (assert (ultimo-movimiento north))
    (assert (casilla-actual (- ?x 1) ?y))
    (assert (casilla-visitada (- ?x 1) ?y))
    (moveWilly north)
)
